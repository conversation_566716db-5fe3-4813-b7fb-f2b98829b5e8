{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/gridfs/index.ts"], "names": [], "mappings": ";;;AAIA,oCAAyE;AACzE,gDAAgE;AAGhE,wCAAgD;AAChD,oCAAgD;AAChD,oDAA0E;AAE1E,yCAKoB;AACpB,qCAIkB;AAElB,MAAM,6BAA6B,GAG/B;IACF,UAAU,EAAE,IAAI;IAChB,cAAc,EAAE,GAAG,GAAG,IAAI;CAC3B,CAAC;AAuCF;;;GAGG;AACH,MAAa,YAAa,SAAQ,+BAAqC;IAcrE,YAAY,EAAM,EAAE,OAA6B;QAC/C,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,YAAI,CAAC,CAAC;QACvB,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,cAAc,GAAG,IAAA,sBAAc,EAAC,EAAE,EAAE;YACxC,GAAG,6BAA6B;YAChC,GAAG,OAAO;YACV,YAAY,EAAE,4BAAY,CAAC,WAAW,CAAC,OAAO,CAAC;SAChD,CAAC,CAAC;QACH,IAAI,CAAC,CAAC,GAAG;YACP,EAAE;YACF,OAAO,EAAE,cAAc;YACvB,iBAAiB,EAAE,EAAE,CAAC,UAAU,CAAc,cAAc,CAAC,UAAU,GAAG,SAAS,CAAC;YACpF,gBAAgB,EAAE,EAAE,CAAC,UAAU,CAAa,cAAc,CAAC,UAAU,GAAG,QAAQ,CAAC;YACjF,cAAc,EAAE,KAAK;YACrB,sBAAsB,EAAE,KAAK;SAC9B,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IAEH,gBAAgB,CACd,QAAgB,EAChB,OAAwC;QAExC,OAAO,IAAI,gCAAuB,CAAC,IAAI,EAAE,QAAQ,EAAE;YACjD,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS;YACnC,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,sBAAsB,CACpB,EAAY,EACZ,QAAgB,EAChB,OAAwC;QAExC,OAAO,IAAI,gCAAuB,CAAC,IAAI,EAAE,QAAQ,EAAE;YACjD,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS;YACnC,GAAG,OAAO;YACV,EAAE;SACH,CAAC,CAAC;IACL,CAAC;IAED,8FAA8F;IAC9F,kBAAkB,CAChB,EAAY,EACZ,OAAuC;QAEvC,OAAO,IAAI,iCAAsB,CAC/B,IAAI,CAAC,CAAC,CAAC,iBAAiB,EACxB,IAAI,CAAC,CAAC,CAAC,gBAAgB,EACvB,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,EAC7B,EAAE,GAAG,EAAE,EAAE,EAAE,EACX,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,OAAO,EAAE,CACpD,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,MAAM,CAAC,EAAY,EAAE,OAA+B;QACxD,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,sBAAc,EAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACzD,IAAI,cAAc,GAAmC,SAAS,CAAC;QAE/D,IAAI,SAAS,EAAE,CAAC;YACd,cAAc,GAAG,IAAI,4BAAkB,CAAC;gBACtC,SAAS;gBACT,wBAAwB,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB;aAC9E,CAAC,CAAC;QACL,CAAC;QAED,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,SAAS,CAC9D,EAAE,GAAG,EAAE,EAAE,EAAE,EACX,EAAE,SAAS,EAAE,cAAc,EAAE,eAAe,EAAE,CAC/C,CAAC;QAEF,MAAM,eAAe,GAAG,cAAc,EAAE,eAAe,CAAC;QACxD,IAAI,eAAe,IAAI,IAAI,IAAI,eAAe,IAAI,CAAC;YACjD,MAAM,IAAI,kCAA0B,CAAC,mBAAmB,SAAS,IAAI,CAAC,CAAC;QACzE,uDAAuD;QACvD,MAAM,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,eAAe,EAAE,CAAC,CAAC;QAE5F,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;YACvB,uDAAuD;YACvD,2DAA2D;YAC3D,MAAM,IAAI,yBAAiB,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED,8DAA8D;IAC9D,IAAI,CAAC,SAA6B,EAAE,EAAE,UAAuB,EAAE;QAC7D,OAAO,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED;;;;;;OAMG;IACH,wBAAwB,CACtB,QAAgB,EAChB,OAAmD;QAEnD,IAAI,IAAI,GAAS,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC;QACpC,IAAI,IAAI,GAAG,SAAS,CAAC;QACrB,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;YACxC,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC;gBAC1B,IAAI,GAAG,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;gBACzB,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QACD,OAAO,IAAI,iCAAsB,CAC/B,IAAI,CAAC,CAAC,CAAC,iBAAiB,EACxB,IAAI,CAAC,CAAC,CAAC,gBAAgB,EACvB,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,EAC7B,EAAE,QAAQ,EAAE,EACZ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAChE,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,MAAM,CAAC,EAAY,EAAE,QAAgB,EAAE,OAA+B;QAC1E,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;QAC3B,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,EAAE,CAAC;QACtC,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAC1F,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,yBAAiB,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED,iFAAiF;IACjF,KAAK,CAAC,IAAI,CAAC,OAA+B;QACxC,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,sBAAc,EAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACzD,IAAI,cAAc,GAAmC,SAAS,CAAC;QAE/D,IAAI,SAAS,EAAE,CAAC;YACd,cAAc,GAAG,IAAI,4BAAkB,CAAC;gBACtC,SAAS;gBACT,wBAAwB,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB;aAC9E,CAAC,CAAC;QACL,CAAC;QAED,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,cAAc,CAAC,eAAe,EAAE,CAAC,CAAC;YAClF,MAAM,eAAe,GAAG,cAAc,CAAC,yBAAyB,CAC9D,mBAAmB,SAAS,IAAI,CACjC,CAAC;YACF,MAAM,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAC,CAAC;QACtE,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;YACrC,MAAM,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;;AA7LH,oCA8LC;AA1LC;;;;;;;GAOG;AACa,kBAAK,GAAG,OAAgB,CAAC"}