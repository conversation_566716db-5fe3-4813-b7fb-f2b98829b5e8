"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/TaskItem.tsx":
/*!*************************************!*\
  !*** ./src/components/TaskItem.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TaskContext */ \"(app-pages-browser)/./src/contexts/TaskContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(app-pages-browser)/./src/contexts/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst TaskItem = (param)=>{\n    let { task } = param;\n    _s();\n    const { updateTask, deleteTask } = (0,_contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__.useTask)();\n    const { isDark } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editData, setEditData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: task.title,\n        description: task.description,\n        assignedTo: task.assignedTo,\n        status: task.status\n    });\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'To Do':\n                return 'bg-gray-100 text-gray-700';\n            case 'In Progress':\n                return 'bg-blue-100 text-blue-700';\n            case 'Done':\n                return 'bg-green-100 text-green-700';\n            default:\n                return 'bg-gray-100 text-gray-700';\n        }\n    };\n    const handleStatusChange = (newStatus)=>{\n        updateTask({\n            id: task._id,\n            status: newStatus\n        });\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n    };\n    const handleSave = ()=>{\n        updateTask({\n            id: task._id,\n            ...editData\n        });\n        setIsEditing(false);\n    };\n    const handleCancel = ()=>{\n        setEditData({\n            title: task.title,\n            description: task.description,\n            assignedTo: task.assignedTo,\n            status: task.status\n        });\n        setIsEditing(false);\n    };\n    const handleDelete = ()=>{\n        if (window.confirm('Are you sure you want to delete this task?')) {\n            deleteTask(task._id);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setEditData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    if (isEditing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"border rounded-lg p-4 shadow-sm transition-colors duration-300 \".concat(isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        name: \"title\",\n                        value: editData.title,\n                        onChange: handleChange,\n                        className: \"w-full px-0 py-1 text-lg font-medium border-0 border-b bg-transparent focus:outline-none focus:border-blue-500 transition-colors duration-300 \".concat(isDark ? 'border-gray-600 text-white' : 'border-gray-200 text-gray-900'),\n                        placeholder: \"Task title\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        name: \"description\",\n                        value: editData.description,\n                        onChange: handleChange,\n                        rows: 2,\n                        className: \"w-full px-0 py-1 border-0 bg-transparent focus:outline-none resize-none transition-colors duration-300 \".concat(isDark ? 'text-gray-300' : 'text-gray-600'),\n                        placeholder: \"Add description...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3 pt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                name: \"assignedTo\",\n                                value: editData.assignedTo,\n                                onChange: handleChange,\n                                className: \"flex-1 px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors duration-300 \".concat(isDark ? 'border-gray-600 bg-gray-700 text-white' : 'border-gray-200 bg-white text-gray-900'),\n                                placeholder: \"Assigned to\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                name: \"status\",\n                                value: editData.status,\n                                onChange: handleChange,\n                                className: \"px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors duration-300 \".concat(isDark ? 'border-gray-600 bg-gray-700 text-white' : 'border-gray-200 bg-white text-gray-900'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"To Do\",\n                                        children: \"To Do\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"In Progress\",\n                                        children: \"In Progress\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"Done\",\n                                        children: \"Done\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 pt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSave,\n                                className: \"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors\",\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCancel,\n                                className: \"px-3 py-1 rounded text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors \".concat(isDark ? 'bg-gray-700 text-white hover:bg-gray-600 focus:ring-gray-500' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500'),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"task-item border rounded-lg p-4 hover:shadow-md hover-lift transition-all duration-200 group \".concat(isDark ? 'bg-gray-800 border-gray-700 hover:border-gray-600' : 'bg-white border-gray-200 hover:border-gray-300'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-start mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base font-medium leading-tight transition-colors duration-300 \".concat(isDark ? 'text-white' : 'text-gray-900'),\n                        children: task.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleEdit,\n                                className: \"p-1 rounded transition-colors duration-300 \".concat(isDark ? 'text-gray-500 hover:text-blue-400' : 'text-gray-400 hover:text-blue-600'),\n                                title: \"Edit task\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDelete,\n                                className: \"p-1 rounded transition-colors duration-300 \".concat(isDark ? 'text-gray-500 hover:text-red-400' : 'text-gray-400 hover:text-red-600'),\n                                title: \"Delete task\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, undefined),\n            task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 text-sm mb-3 leading-relaxed\",\n                children: task.description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium text-gray-600\",\n                                    children: task.assignedTo.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: task.assignedTo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: task.status,\n                        onChange: (e)=>handleStatusChange(e.target.value),\n                        className: \"text-xs px-2 py-1 rounded-full border-0 \".concat(getStatusColor(task.status), \" focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"To Do\",\n                                children: \"To Do\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"In Progress\",\n                                children: \"In Progress\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"Done\",\n                                children: \"Done\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-400 mt-3 pt-2 border-t border-gray-100\",\n                children: [\n                    \"Updated \",\n                    new Date(task.updatedAt).toLocaleDateString()\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TaskItem, \"eNno7dLR7NeiZ+OTr6dRZzlEX40=\", false, function() {\n    return [\n        _contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__.useTask,\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme\n    ];\n});\n_c = TaskItem;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaskItem);\nvar _c;\n$RefreshReg$(_c, \"TaskItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1Rhc2tJdGVtLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUV3QztBQUNTO0FBQ0U7QUFPbkQsTUFBTUksV0FBb0M7UUFBQyxFQUFFQyxJQUFJLEVBQUU7O0lBQ2pELE1BQU0sRUFBRUMsVUFBVSxFQUFFQyxVQUFVLEVBQUUsR0FBR0wsOERBQU9BO0lBQzFDLE1BQU0sRUFBRU0sTUFBTSxFQUFFLEdBQUdMLGdFQUFRQTtJQUMzQixNQUFNLENBQUNNLFdBQVdDLGFBQWEsR0FBR1QsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDVSxVQUFVQyxZQUFZLEdBQUdYLCtDQUFRQSxDQUFDO1FBQ3ZDWSxPQUFPUixLQUFLUSxLQUFLO1FBQ2pCQyxhQUFhVCxLQUFLUyxXQUFXO1FBQzdCQyxZQUFZVixLQUFLVSxVQUFVO1FBQzNCQyxRQUFRWCxLQUFLVyxNQUFNO0lBQ3JCO0lBRUEsTUFBTUMsaUJBQWlCLENBQUNEO1FBQ3RCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1Q7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxNQUFNRSxxQkFBcUIsQ0FBQ0M7UUFDMUJiLFdBQVc7WUFDVGMsSUFBSWYsS0FBS2dCLEdBQUc7WUFDWkwsUUFBUUc7UUFDVjtJQUNGO0lBRUEsTUFBTUcsYUFBYTtRQUNqQlosYUFBYTtJQUNmO0lBRUEsTUFBTWEsYUFBYTtRQUNqQmpCLFdBQVc7WUFDVGMsSUFBSWYsS0FBS2dCLEdBQUc7WUFDWixHQUFHVixRQUFRO1FBQ2I7UUFDQUQsYUFBYTtJQUNmO0lBRUEsTUFBTWMsZUFBZTtRQUNuQlosWUFBWTtZQUNWQyxPQUFPUixLQUFLUSxLQUFLO1lBQ2pCQyxhQUFhVCxLQUFLUyxXQUFXO1lBQzdCQyxZQUFZVixLQUFLVSxVQUFVO1lBQzNCQyxRQUFRWCxLQUFLVyxNQUFNO1FBQ3JCO1FBQ0FOLGFBQWE7SUFDZjtJQUVBLE1BQU1lLGVBQWU7UUFDbkIsSUFBSUMsT0FBT0MsT0FBTyxDQUFDLCtDQUErQztZQUNoRXBCLFdBQVdGLEtBQUtnQixHQUFHO1FBQ3JCO0lBQ0Y7SUFFQSxNQUFNTyxlQUFlLENBQUNDO1FBQ3BCLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBR0YsRUFBRUcsTUFBTTtRQUNoQ3BCLFlBQVlxQixDQUFBQSxPQUFTO2dCQUNuQixHQUFHQSxJQUFJO2dCQUNQLENBQUNILEtBQUssRUFBRUM7WUFDVjtJQUNGO0lBRUEsSUFBSXRCLFdBQVc7UUFDYixxQkFDRSw4REFBQ3lCO1lBQUlDLFdBQVcsa0VBSWYsT0FIQzNCLFNBQ0ksZ0NBQ0E7c0JBRUosNEVBQUMwQjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUNDQyxNQUFLO3dCQUNMUCxNQUFLO3dCQUNMQyxPQUFPcEIsU0FBU0UsS0FBSzt3QkFDckJ5QixVQUFVVjt3QkFDVk8sV0FBVyxpSkFJVixPQUhDM0IsU0FDSSwrQkFDQTt3QkFFTitCLGFBQVk7Ozs7OztrQ0FHZCw4REFBQ0M7d0JBQ0NWLE1BQUs7d0JBQ0xDLE9BQU9wQixTQUFTRyxXQUFXO3dCQUMzQndCLFVBQVVWO3dCQUNWYSxNQUFNO3dCQUNOTixXQUFXLDBHQUVWLE9BREMzQixTQUFTLGtCQUFrQjt3QkFFN0IrQixhQUFZOzs7Ozs7a0NBR2QsOERBQUNMO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQ0NDLE1BQUs7Z0NBQ0xQLE1BQUs7Z0NBQ0xDLE9BQU9wQixTQUFTSSxVQUFVO2dDQUMxQnVCLFVBQVVWO2dDQUNWTyxXQUFXLDhIQUlWLE9BSEMzQixTQUNJLDJDQUNBO2dDQUVOK0IsYUFBWTs7Ozs7OzBDQUdkLDhEQUFDRztnQ0FDQ1osTUFBSztnQ0FDTEMsT0FBT3BCLFNBQVNLLE1BQU07Z0NBQ3RCc0IsVUFBVVY7Z0NBQ1ZPLFdBQVcsdUhBSVYsT0FIQzNCLFNBQ0ksMkNBQ0E7O2tEQUdOLDhEQUFDbUM7d0NBQU9aLE9BQU07a0RBQVE7Ozs7OztrREFDdEIsOERBQUNZO3dDQUFPWixPQUFNO2tEQUFjOzs7Ozs7a0RBQzVCLDhEQUFDWTt3Q0FBT1osT0FBTTtrREFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUl6Qiw4REFBQ0c7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDUztnQ0FDQ0MsU0FBU3RCO2dDQUNUWSxXQUFVOzBDQUNYOzs7Ozs7MENBR0QsOERBQUNTO2dDQUNDQyxTQUFTckI7Z0NBQ1RXLFdBQVcsbUdBSVYsT0FIQzNCLFNBQ0ksaUVBQ0E7MENBRVA7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBT1g7SUFFQSxxQkFDRSw4REFBQzBCO1FBQUlDLFdBQVcsZ0dBSWYsT0FIQzNCLFNBQ0ksc0RBQ0E7OzBCQUdKLDhEQUFDMEI7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDVzt3QkFBR1gsV0FBVyxzRUFFZCxPQURDM0IsU0FBUyxlQUFlO2tDQUNyQkgsS0FBS1EsS0FBSzs7Ozs7O2tDQUNmLDhEQUFDcUI7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDUztnQ0FDQ0MsU0FBU3ZCO2dDQUNUYSxXQUFXLDhDQUlWLE9BSEMzQixTQUNJLHNDQUNBO2dDQUVOSyxPQUFNOzBDQUVOLDRFQUFDa0M7b0NBQUlaLFdBQVU7b0NBQVVhLE1BQUs7b0NBQU9DLFFBQU87b0NBQWVDLFNBQVE7OENBQ2pFLDRFQUFDQzt3Q0FBS0MsZUFBYzt3Q0FBUUMsZ0JBQWU7d0NBQVFDLGFBQWE7d0NBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7MENBR3pFLDhEQUFDWDtnQ0FDQ0MsU0FBU3BCO2dDQUNUVSxXQUFXLDhDQUlWLE9BSEMzQixTQUNJLHFDQUNBO2dDQUVOSyxPQUFNOzBDQUVOLDRFQUFDa0M7b0NBQUlaLFdBQVU7b0NBQVVhLE1BQUs7b0NBQU9DLFFBQU87b0NBQWVDLFNBQVE7OENBQ2pFLDRFQUFDQzt3Q0FBS0MsZUFBYzt3Q0FBUUMsZ0JBQWU7d0NBQVFDLGFBQWE7d0NBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFPNUVsRCxLQUFLUyxXQUFXLGtCQUNmLDhEQUFDMEM7Z0JBQUVyQixXQUFVOzBCQUE4QzlCLEtBQUtTLFdBQVc7Ozs7OzswQkFJN0UsOERBQUNvQjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNzQjtvQ0FBS3RCLFdBQVU7OENBQ2I5QixLQUFLVSxVQUFVLENBQUMyQyxNQUFNLENBQUMsR0FBR0MsV0FBVzs7Ozs7Ozs7Ozs7MENBRzFDLDhEQUFDRjtnQ0FBS3RCLFdBQVU7MENBQXlCOUIsS0FBS1UsVUFBVTs7Ozs7Ozs7Ozs7O2tDQUcxRCw4REFBQzJCO3dCQUNDWCxPQUFPMUIsS0FBS1csTUFBTTt3QkFDbEJzQixVQUFVLENBQUNULElBQU1YLG1CQUFtQlcsRUFBRUcsTUFBTSxDQUFDRCxLQUFLO3dCQUNsREksV0FBVywyQ0FBdUUsT0FBNUJsQixlQUFlWixLQUFLVyxNQUFNLEdBQUU7OzBDQUVsRiw4REFBQzJCO2dDQUFPWixPQUFNOzBDQUFROzs7Ozs7MENBQ3RCLDhEQUFDWTtnQ0FBT1osT0FBTTswQ0FBYzs7Ozs7OzBDQUM1Qiw4REFBQ1k7Z0NBQU9aLE9BQU07MENBQU87Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFLekIsOERBQUNHO2dCQUFJQyxXQUFVOztvQkFBMkQ7b0JBQy9ELElBQUl5QixLQUFLdkQsS0FBS3dELFNBQVMsRUFBRUMsa0JBQWtCOzs7Ozs7Ozs7Ozs7O0FBSTVEO0dBbE9NMUQ7O1FBQytCRiwwREFBT0E7UUFDdkJDLDREQUFRQTs7O0tBRnZCQztBQW9PTixpRUFBZUEsUUFBUUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhcmtpdFxcRGVza3RvcFxcYXNzZXNtZW50XFx0YXNrLW1hbmFnZXJcXHNyY1xcY29tcG9uZW50c1xcVGFza0l0ZW0udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlVGFzayB9IGZyb20gJ0AvY29udGV4dHMvVGFza0NvbnRleHQnO1xuaW1wb3J0IHsgdXNlVGhlbWUgfSBmcm9tICdAL2NvbnRleHRzL1RoZW1lQ29udGV4dCc7XG5pbXBvcnQgeyBUYXNrLCBUYXNrU3RhdHVzIH0gZnJvbSAnQC90eXBlcyc7XG5cbmludGVyZmFjZSBUYXNrSXRlbVByb3BzIHtcbiAgdGFzazogVGFzaztcbn1cblxuY29uc3QgVGFza0l0ZW06IFJlYWN0LkZDPFRhc2tJdGVtUHJvcHM+ID0gKHsgdGFzayB9KSA9PiB7XG4gIGNvbnN0IHsgdXBkYXRlVGFzaywgZGVsZXRlVGFzayB9ID0gdXNlVGFzaygpO1xuICBjb25zdCB7IGlzRGFyayB9ID0gdXNlVGhlbWUoKTtcbiAgY29uc3QgW2lzRWRpdGluZywgc2V0SXNFZGl0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2VkaXREYXRhLCBzZXRFZGl0RGF0YV0gPSB1c2VTdGF0ZSh7XG4gICAgdGl0bGU6IHRhc2sudGl0bGUsXG4gICAgZGVzY3JpcHRpb246IHRhc2suZGVzY3JpcHRpb24sXG4gICAgYXNzaWduZWRUbzogdGFzay5hc3NpZ25lZFRvLFxuICAgIHN0YXR1czogdGFzay5zdGF0dXMsXG4gIH0pO1xuXG4gIGNvbnN0IGdldFN0YXR1c0NvbG9yID0gKHN0YXR1czogVGFza1N0YXR1cykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdUbyBEbyc6XG4gICAgICAgIHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTcwMCc7XG4gICAgICBjYXNlICdJbiBQcm9ncmVzcyc6XG4gICAgICAgIHJldHVybiAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTcwMCc7XG4gICAgICBjYXNlICdEb25lJzpcbiAgICAgICAgcmV0dXJuICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi03MDAnO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNzAwJztcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU3RhdHVzQ2hhbmdlID0gKG5ld1N0YXR1czogVGFza1N0YXR1cykgPT4ge1xuICAgIHVwZGF0ZVRhc2soe1xuICAgICAgaWQ6IHRhc2suX2lkLFxuICAgICAgc3RhdHVzOiBuZXdTdGF0dXMsXG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRWRpdCA9ICgpID0+IHtcbiAgICBzZXRJc0VkaXRpbmcodHJ1ZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2F2ZSA9ICgpID0+IHtcbiAgICB1cGRhdGVUYXNrKHtcbiAgICAgIGlkOiB0YXNrLl9pZCxcbiAgICAgIC4uLmVkaXREYXRhLFxuICAgIH0pO1xuICAgIHNldElzRWRpdGluZyhmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ2FuY2VsID0gKCkgPT4ge1xuICAgIHNldEVkaXREYXRhKHtcbiAgICAgIHRpdGxlOiB0YXNrLnRpdGxlLFxuICAgICAgZGVzY3JpcHRpb246IHRhc2suZGVzY3JpcHRpb24sXG4gICAgICBhc3NpZ25lZFRvOiB0YXNrLmFzc2lnbmVkVG8sXG4gICAgICBzdGF0dXM6IHRhc2suc3RhdHVzLFxuICAgIH0pO1xuICAgIHNldElzRWRpdGluZyhmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRGVsZXRlID0gKCkgPT4ge1xuICAgIGlmICh3aW5kb3cuY29uZmlybSgnQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSB0aGlzIHRhc2s/JykpIHtcbiAgICAgIGRlbGV0ZVRhc2sodGFzay5faWQpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDaGFuZ2UgPSAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudCB8IEhUTUxUZXh0QXJlYUVsZW1lbnQgfCBIVE1MU2VsZWN0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCB7IG5hbWUsIHZhbHVlIH0gPSBlLnRhcmdldDtcbiAgICBzZXRFZGl0RGF0YShwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgW25hbWVdOiB2YWx1ZSxcbiAgICB9KSk7XG4gIH07XG5cbiAgaWYgKGlzRWRpdGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YGJvcmRlciByb3VuZGVkLWxnIHAtNCBzaGFkb3ctc20gdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwICR7XG4gICAgICAgIGlzRGFya1xuICAgICAgICAgID8gJ2JnLWdyYXktODAwIGJvcmRlci1ncmF5LTcwMCdcbiAgICAgICAgICA6ICdiZy13aGl0ZSBib3JkZXItZ3JheS0yMDAnXG4gICAgICB9YH0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICBuYW1lPVwidGl0bGVcIlxuICAgICAgICAgICAgdmFsdWU9e2VkaXREYXRhLnRpdGxlfVxuICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNoYW5nZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBweC0wIHB5LTEgdGV4dC1sZyBmb250LW1lZGl1bSBib3JkZXItMCBib3JkZXItYiBiZy10cmFuc3BhcmVudCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgICAgICBpc0RhcmtcbiAgICAgICAgICAgICAgICA/ICdib3JkZXItZ3JheS02MDAgdGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS0yMDAgdGV4dC1ncmF5LTkwMCdcbiAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJUYXNrIHRpdGxlXCJcbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICBuYW1lPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgdmFsdWU9e2VkaXREYXRhLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNoYW5nZX1cbiAgICAgICAgICAgIHJvd3M9ezJ9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgcHgtMCBweS0xIGJvcmRlci0wIGJnLXRyYW5zcGFyZW50IGZvY3VzOm91dGxpbmUtbm9uZSByZXNpemUtbm9uZSB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICAgICAgaXNEYXJrID8gJ3RleHQtZ3JheS0zMDAnIDogJ3RleHQtZ3JheS02MDAnXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQWRkIGRlc2NyaXB0aW9uLi4uXCJcbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0zIHB0LTJcIj5cbiAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgIG5hbWU9XCJhc3NpZ25lZFRvXCJcbiAgICAgICAgICAgICAgdmFsdWU9e2VkaXREYXRhLmFzc2lnbmVkVG99XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtMSBweC0yIHB5LTEgdGV4dC1zbSBib3JkZXIgcm91bmRlZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0xIGZvY3VzOnJpbmctYmx1ZS01MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgaXNEYXJrXG4gICAgICAgICAgICAgICAgICA/ICdib3JkZXItZ3JheS02MDAgYmctZ3JheS03MDAgdGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTIwMCBiZy13aGl0ZSB0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJBc3NpZ25lZCB0b1wiXG4gICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgIG5hbWU9XCJzdGF0dXNcIlxuICAgICAgICAgICAgICB2YWx1ZT17ZWRpdERhdGEuc3RhdHVzfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0yIHB5LTEgdGV4dC1zbSBib3JkZXIgcm91bmRlZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0xIGZvY3VzOnJpbmctYmx1ZS01MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgaXNEYXJrXG4gICAgICAgICAgICAgICAgICA/ICdib3JkZXItZ3JheS02MDAgYmctZ3JheS03MDAgdGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTIwMCBiZy13aGl0ZSB0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlRvIERvXCI+VG8gRG88L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkluIFByb2dyZXNzXCI+SW4gUHJvZ3Jlc3M8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkRvbmVcIj5Eb25lPC9vcHRpb24+XG4gICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMiBwdC0yXCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNhdmV9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcHgtMyBweS0xIHJvdW5kZWQgdGV4dC1zbSBob3ZlcjpiZy1ibHVlLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6cmluZy1vZmZzZXQtMiB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIFNhdmVcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDYW5jZWx9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTMgcHktMSByb3VuZGVkIHRleHQtc20gZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgaXNEYXJrXG4gICAgICAgICAgICAgICAgICA/ICdiZy1ncmF5LTcwMCB0ZXh0LXdoaXRlIGhvdmVyOmJnLWdyYXktNjAwIGZvY3VzOnJpbmctZ3JheS01MDAnXG4gICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMjAwIGZvY3VzOnJpbmctZ3JheS01MDAnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgdGFzay1pdGVtIGJvcmRlciByb3VuZGVkLWxnIHAtNCBob3ZlcjpzaGFkb3ctbWQgaG92ZXItbGlmdCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZ3JvdXAgJHtcbiAgICAgIGlzRGFya1xuICAgICAgICA/ICdiZy1ncmF5LTgwMCBib3JkZXItZ3JheS03MDAgaG92ZXI6Ym9yZGVyLWdyYXktNjAwJ1xuICAgICAgICA6ICdiZy13aGl0ZSBib3JkZXItZ3JheS0yMDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xuICAgIH1gfT5cbiAgICAgIHsvKiBIZWFkZXIgd2l0aCB0aXRsZSBhbmQgYWN0aW9ucyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnQgbWItMlwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPXtgdGV4dC1iYXNlIGZvbnQtbWVkaXVtIGxlYWRpbmctdGlnaHQgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgaXNEYXJrID8gJ3RleHQtd2hpdGUnIDogJ3RleHQtZ3JheS05MDAnXG4gICAgICAgIH1gfT57dGFzay50aXRsZX08L2gzPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTEgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eVwiPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUVkaXR9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2BwLTEgcm91bmRlZCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICAgICAgaXNEYXJrXG4gICAgICAgICAgICAgICAgPyAndGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWJsdWUtNDAwJ1xuICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ibHVlLTYwMCdcbiAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgdGl0bGU9XCJFZGl0IHRhc2tcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTEgNUg2YTIgMiAwIDAwLTIgMnYxMWEyIDIgMCAwMDIgMmgxMWEyIDIgMCAwMDItMnYtNW0tMS40MTQtOS40MTRhMiAyIDAgMTEyLjgyOCAyLjgyOEwxMS44MjggMTVIOXYtMi44MjhsOC41ODYtOC41ODZ6XCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZURlbGV0ZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtMSByb3VuZGVkIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgICAgICBpc0RhcmtcbiAgICAgICAgICAgICAgICA/ICd0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtcmVkLTQwMCdcbiAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtcmVkLTYwMCdcbiAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgdGl0bGU9XCJEZWxldGUgdGFza1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xOSA3bC0uODY3IDEyLjE0MkEyIDIgMCAwMTE2LjEzOCAyMUg3Ljg2MmEyIDIgMCAwMS0xLjk5NS0xLjg1OEw1IDdtNSA0djZtNC02djZtMS0xMFY0YTEgMSAwIDAwLTEtMWgtNGExIDEgMCAwMC0xIDF2M000IDdoMTZcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBEZXNjcmlwdGlvbiAqL31cbiAgICAgIHt0YXNrLmRlc2NyaXB0aW9uICYmIChcbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LXNtIG1iLTMgbGVhZGluZy1yZWxheGVkXCI+e3Rhc2suZGVzY3JpcHRpb259PC9wPlxuICAgICAgKX1cblxuICAgICAgey8qIEZvb3RlciB3aXRoIGFzc2lnbmVlIGFuZCBzdGF0dXMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTYgaC02IGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIHt0YXNrLmFzc2lnbmVkVG8uY2hhckF0KDApLnRvVXBwZXJDYXNlKCl9XG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+e3Rhc2suYXNzaWduZWRUb308L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxzZWxlY3RcbiAgICAgICAgICB2YWx1ZT17dGFzay5zdGF0dXN9XG4gICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVTdGF0dXNDaGFuZ2UoZS50YXJnZXQudmFsdWUgYXMgVGFza1N0YXR1cyl9XG4gICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC14cyBweC0yIHB5LTEgcm91bmRlZC1mdWxsIGJvcmRlci0wICR7Z2V0U3RhdHVzQ29sb3IodGFzay5zdGF0dXMpfSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgY3Vyc29yLXBvaW50ZXJgfVxuICAgICAgICA+XG4gICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlRvIERvXCI+VG8gRG88L29wdGlvbj5cbiAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiSW4gUHJvZ3Jlc3NcIj5JbiBQcm9ncmVzczwvb3B0aW9uPlxuICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJEb25lXCI+RG9uZTwvb3B0aW9uPlxuICAgICAgICA8L3NlbGVjdD5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogVGltZXN0YW1wICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgbXQtMyBwdC0yIGJvcmRlci10IGJvcmRlci1ncmF5LTEwMFwiPlxuICAgICAgICBVcGRhdGVkIHtuZXcgRGF0ZSh0YXNrLnVwZGF0ZWRBdCkudG9Mb2NhbGVEYXRlU3RyaW5nKCl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFRhc2tJdGVtO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VUYXNrIiwidXNlVGhlbWUiLCJUYXNrSXRlbSIsInRhc2siLCJ1cGRhdGVUYXNrIiwiZGVsZXRlVGFzayIsImlzRGFyayIsImlzRWRpdGluZyIsInNldElzRWRpdGluZyIsImVkaXREYXRhIiwic2V0RWRpdERhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiYXNzaWduZWRUbyIsInN0YXR1cyIsImdldFN0YXR1c0NvbG9yIiwiaGFuZGxlU3RhdHVzQ2hhbmdlIiwibmV3U3RhdHVzIiwiaWQiLCJfaWQiLCJoYW5kbGVFZGl0IiwiaGFuZGxlU2F2ZSIsImhhbmRsZUNhbmNlbCIsImhhbmRsZURlbGV0ZSIsIndpbmRvdyIsImNvbmZpcm0iLCJoYW5kbGVDaGFuZ2UiLCJlIiwibmFtZSIsInZhbHVlIiwidGFyZ2V0IiwicHJldiIsImRpdiIsImNsYXNzTmFtZSIsImlucHV0IiwidHlwZSIsIm9uQ2hhbmdlIiwicGxhY2Vob2xkZXIiLCJ0ZXh0YXJlYSIsInJvd3MiLCJzZWxlY3QiLCJvcHRpb24iLCJidXR0b24iLCJvbkNsaWNrIiwiaDMiLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJwIiwic3BhbiIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwiRGF0ZSIsInVwZGF0ZWRBdCIsInRvTG9jYWxlRGF0ZVN0cmluZyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TaskItem.tsx\n"));

/***/ })

});