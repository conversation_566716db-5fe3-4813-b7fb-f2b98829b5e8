export type TaskStatus = "To Do" | "In Progress" | "Done";

export interface Task {
  id: string;
  title: string;
  description: string;
  assignedTo: string;
  status: TaskStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateTaskInput {
  title: string;
  description: string;
  assignedTo: string;
  status: TaskStatus;
}

export interface UpdateTaskInput {
  id: string;
  title?: string;
  description?: string;
  assignedTo?: string;
  status?: TaskStatus;
}

export interface TaskFilters {
  status?: TaskStatus;
  assignedTo?: string;
}
