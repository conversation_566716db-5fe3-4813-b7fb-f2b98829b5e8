'use client';

import React, { useState } from 'react';
import { useTask } from '@/contexts/TaskContext';
import { TaskStatus } from '@/types';
import TaskItem from './TaskItem';

const TaskList: React.FC = () => {
  const { state, setFilters, clearFilters, getFilteredTasks } = useTask();
  const [statusFilter, setStatusFilter] = useState<TaskStatus | ''>('');
  const [assigneeFilter, setAssigneeFilter] = useState('');

  const filteredTasks = getFilteredTasks();

  const handleStatusFilterChange = (status: TaskStatus | '') => {
    setStatusFilter(status);
    const newFilters = {
      ...state.filters,
      status: status || undefined,
    };
    if (!status) {
      delete newFilters.status;
    }
    setFilters(newFilters);
  };

  const handleAssigneeFilterChange = (assignee: string) => {
    setAssigneeFilter(assignee);
    const newFilters = {
      ...state.filters,
      assignedTo: assignee || undefined,
    };
    if (!assignee) {
      delete newFilters.assignedTo;
    }
    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    setStatusFilter('');
    setAssigneeFilter('');
    clearFilters();
  };

  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case 'To Do':
        return 'bg-gray-100 text-gray-800';
      case 'In Progress':
        return 'bg-blue-100 text-blue-800';
      case 'Done':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const uniqueAssignees = Array.from(new Set(state.tasks.map(task => task.assignedTo)));

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Tasks</h2>
        <div className="text-sm text-gray-600">
          {filteredTasks.length} of {state.tasks.length} tasks
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3 text-gray-700">Filters</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="statusFilter" className="block text-sm font-medium text-gray-700 mb-1">
              Filter by Status
            </label>
            <select
              id="statusFilter"
              value={statusFilter}
              onChange={(e) => handleStatusFilterChange(e.target.value as TaskStatus | '')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="To Do">To Do</option>
              <option value="In Progress">In Progress</option>
              <option value="Done">Done</option>
            </select>
          </div>

          <div>
            <label htmlFor="assigneeFilter" className="block text-sm font-medium text-gray-700 mb-1">
              Filter by Assignee
            </label>
            <select
              id="assigneeFilter"
              value={assigneeFilter}
              onChange={(e) => handleAssigneeFilterChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Assignees</option>
              {uniqueAssignees.map(assignee => (
                <option key={assignee} value={assignee}>
                  {assignee}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={handleClearFilters}
              className="w-full bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {state.error && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
          {state.error}
        </div>
      )}

      {/* Loading Indicator */}
      {state.loading && (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-gray-600">Loading tasks...</p>
        </div>
      )}

      {/* Task List */}
      <div className="space-y-4">
        {!state.loading && filteredTasks.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {state.tasks.length === 0 ? (
              <p>No tasks yet. Create your first task!</p>
            ) : (
              <p>No tasks match the current filters.</p>
            )}
          </div>
        ) : (
          filteredTasks.map(task => (
            <TaskItem key={task._id} task={task} />
          ))
        )}
      </div>
    </div>
  );
};

export default TaskList;
