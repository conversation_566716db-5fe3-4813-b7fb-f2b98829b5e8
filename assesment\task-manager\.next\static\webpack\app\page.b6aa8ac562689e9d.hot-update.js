"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/TaskItem.tsx":
/*!*************************************!*\
  !*** ./src/components/TaskItem.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TaskContext */ \"(app-pages-browser)/./src/contexts/TaskContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(app-pages-browser)/./src/contexts/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst TaskItem = (param)=>{\n    let { task } = param;\n    _s();\n    const { updateTask, deleteTask } = (0,_contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__.useTask)();\n    const { isDark } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editData, setEditData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: task.title,\n        description: task.description,\n        assignedTo: task.assignedTo,\n        status: task.status\n    });\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'To Do':\n                return 'bg-gray-100 text-gray-700';\n            case 'In Progress':\n                return 'bg-blue-100 text-blue-700';\n            case 'Done':\n                return 'bg-green-100 text-green-700';\n            default:\n                return 'bg-gray-100 text-gray-700';\n        }\n    };\n    const handleStatusChange = (newStatus)=>{\n        updateTask({\n            id: task._id,\n            status: newStatus\n        });\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n    };\n    const handleSave = ()=>{\n        updateTask({\n            id: task._id,\n            ...editData\n        });\n        setIsEditing(false);\n    };\n    const handleCancel = ()=>{\n        setEditData({\n            title: task.title,\n            description: task.description,\n            assignedTo: task.assignedTo,\n            status: task.status\n        });\n        setIsEditing(false);\n    };\n    const handleDelete = ()=>{\n        if (window.confirm('Are you sure you want to delete this task?')) {\n            deleteTask(task._id);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setEditData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    if (isEditing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"border rounded-lg p-4 shadow-sm transition-colors duration-300 \".concat(isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        name: \"title\",\n                        value: editData.title,\n                        onChange: handleChange,\n                        className: \"w-full px-0 py-1 text-lg font-medium border-0 border-b bg-transparent focus:outline-none focus:border-blue-500 transition-colors duration-300 \".concat(isDark ? 'border-gray-600 text-white' : 'border-gray-200 text-gray-900'),\n                        placeholder: \"Task title\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        name: \"description\",\n                        value: editData.description,\n                        onChange: handleChange,\n                        rows: 2,\n                        className: \"w-full px-0 py-1 border-0 bg-transparent focus:outline-none resize-none transition-colors duration-300 \".concat(isDark ? 'text-gray-300' : 'text-gray-600'),\n                        placeholder: \"Add description...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3 pt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                name: \"assignedTo\",\n                                value: editData.assignedTo,\n                                onChange: handleChange,\n                                className: \"flex-1 px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors duration-300 \".concat(isDark ? 'border-gray-600 bg-gray-700 text-white' : 'border-gray-200 bg-white text-gray-900'),\n                                placeholder: \"Assigned to\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                name: \"status\",\n                                value: editData.status,\n                                onChange: handleChange,\n                                className: \"px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors duration-300 \".concat(isDark ? 'border-gray-600 bg-gray-700 text-white' : 'border-gray-200 bg-white text-gray-900'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"To Do\",\n                                        children: \"To Do\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"In Progress\",\n                                        children: \"In Progress\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"Done\",\n                                        children: \"Done\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 pt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSave,\n                                className: \"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors\",\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCancel,\n                                className: \"px-3 py-1 rounded text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors \".concat(isDark ? 'bg-gray-700 text-white hover:bg-gray-600 focus:ring-gray-500' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500'),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"task-item border rounded-lg p-4 hover:shadow-md hover-lift transition-all duration-200 group \".concat(isDark ? 'bg-gray-800 border-gray-700 hover:border-gray-600' : 'bg-white border-gray-200 hover:border-gray-300'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-start mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base font-medium leading-tight transition-colors duration-300 \".concat(isDark ? 'text-white' : 'text-gray-900'),\n                        children: task.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleEdit,\n                                className: \"text-gray-400 hover:text-blue-600 p-1 rounded\",\n                                title: \"Edit task\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDelete,\n                                className: \"text-gray-400 hover:text-red-600 p-1 rounded\",\n                                title: \"Delete task\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, undefined),\n            task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 text-sm mb-3 leading-relaxed\",\n                children: task.description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 199,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium text-gray-600\",\n                                    children: task.assignedTo.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: task.assignedTo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: task.status,\n                        onChange: (e)=>handleStatusChange(e.target.value),\n                        className: \"text-xs px-2 py-1 rounded-full border-0 \".concat(getStatusColor(task.status), \" focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"To Do\",\n                                children: \"To Do\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"In Progress\",\n                                children: \"In Progress\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"Done\",\n                                children: \"Done\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-400 mt-3 pt-2 border-t border-gray-100\",\n                children: [\n                    \"Updated \",\n                    new Date(task.updatedAt).toLocaleDateString()\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TaskItem, \"eNno7dLR7NeiZ+OTr6dRZzlEX40=\", false, function() {\n    return [\n        _contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__.useTask,\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme\n    ];\n});\n_c = TaskItem;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaskItem);\nvar _c;\n$RefreshReg$(_c, \"TaskItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TaskItem.tsx\n"));

/***/ })

});