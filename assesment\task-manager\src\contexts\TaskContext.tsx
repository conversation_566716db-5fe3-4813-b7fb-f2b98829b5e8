'use client';

import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { Task, CreateTaskInput, UpdateTaskInput, TaskFilters } from '@/types';

interface TaskState {
  tasks: Task[];
  filters: TaskFilters;
}

type TaskAction =
  | { type: 'ADD_TASK'; payload: CreateTaskInput }
  | { type: 'UPDATE_TASK'; payload: UpdateTaskInput }
  | { type: 'DELETE_TASK'; payload: string }
  | { type: 'SET_FILTERS'; payload: TaskFilters }
  | { type: 'CLEAR_FILTERS' };

interface TaskContextType {
  state: TaskState;
  addTask: (task: CreateTaskInput) => void;
  updateTask: (task: UpdateTaskInput) => void;
  deleteTask: (id: string) => void;
  setFilters: (filters: TaskFilters) => void;
  clearFilters: () => void;
  getFilteredTasks: () => Task[];
}

const TaskContext = createContext<TaskContextType | undefined>(undefined);

const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

const taskReducer = (state: TaskState, action: TaskAction): TaskState => {
  switch (action.type) {
    case 'ADD_TASK': {
      const newTask: Task = {
        id: generateId(),
        ...action.payload,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      return {
        ...state,
        tasks: [...state.tasks, newTask],
      };
    }
    case 'UPDATE_TASK': {
      const updatedTasks = state.tasks.map(task =>
        task.id === action.payload.id
          ? { ...task, ...action.payload, updatedAt: new Date() }
          : task
      );
      return {
        ...state,
        tasks: updatedTasks,
      };
    }
    case 'DELETE_TASK': {
      const filteredTasks = state.tasks.filter(task => task.id !== action.payload);
      return {
        ...state,
        tasks: filteredTasks,
      };
    }
    case 'SET_FILTERS': {
      return {
        ...state,
        filters: action.payload,
      };
    }
    case 'CLEAR_FILTERS': {
      return {
        ...state,
        filters: {},
      };
    }
    default:
      return state;
  }
};

const initialState: TaskState = {
  tasks: [],
  filters: {},
};

export const TaskProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(taskReducer, initialState);

  const addTask = (task: CreateTaskInput) => {
    dispatch({ type: 'ADD_TASK', payload: task });
  };

  const updateTask = (task: UpdateTaskInput) => {
    dispatch({ type: 'UPDATE_TASK', payload: task });
  };

  const deleteTask = (id: string) => {
    dispatch({ type: 'DELETE_TASK', payload: id });
  };

  const setFilters = (filters: TaskFilters) => {
    dispatch({ type: 'SET_FILTERS', payload: filters });
  };

  const clearFilters = () => {
    dispatch({ type: 'CLEAR_FILTERS' });
  };

  const getFilteredTasks = (): Task[] => {
    let filteredTasks = state.tasks;

    if (state.filters.status) {
      filteredTasks = filteredTasks.filter(task => task.status === state.filters.status);
    }

    if (state.filters.assignedTo) {
      filteredTasks = filteredTasks.filter(task => 
        task.assignedTo.toLowerCase().includes(state.filters.assignedTo!.toLowerCase())
      );
    }

    return filteredTasks;
  };

  const value: TaskContextType = {
    state,
    addTask,
    updateTask,
    deleteTask,
    setFilters,
    clearFilters,
    getFilteredTasks,
  };

  return <TaskContext.Provider value={value}>{children}</TaskContext.Provider>;
};

export const useTask = (): TaskContextType => {
  const context = useContext(TaskContext);
  if (context === undefined) {
    throw new Error('useTask must be used within a TaskProvider');
  }
  return context;
};
