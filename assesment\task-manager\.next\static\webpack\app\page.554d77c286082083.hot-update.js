"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/TaskItem.tsx":
/*!*************************************!*\
  !*** ./src/components/TaskItem.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TaskContext */ \"(app-pages-browser)/./src/contexts/TaskContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(app-pages-browser)/./src/contexts/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst TaskItem = (param)=>{\n    let { task } = param;\n    _s();\n    const { updateTask, deleteTask } = (0,_contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__.useTask)();\n    const { isDark } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editData, setEditData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: task.title,\n        description: task.description,\n        assignedTo: task.assignedTo,\n        status: task.status\n    });\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'To Do':\n                return 'bg-gray-100 text-gray-700';\n            case 'In Progress':\n                return 'bg-blue-100 text-blue-700';\n            case 'Done':\n                return 'bg-green-100 text-green-700';\n            default:\n                return 'bg-gray-100 text-gray-700';\n        }\n    };\n    const handleStatusChange = (newStatus)=>{\n        updateTask({\n            id: task._id,\n            status: newStatus\n        });\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n    };\n    const handleSave = ()=>{\n        updateTask({\n            id: task._id,\n            ...editData\n        });\n        setIsEditing(false);\n    };\n    const handleCancel = ()=>{\n        setEditData({\n            title: task.title,\n            description: task.description,\n            assignedTo: task.assignedTo,\n            status: task.status\n        });\n        setIsEditing(false);\n    };\n    const handleDelete = ()=>{\n        if (window.confirm('Are you sure you want to delete this task?')) {\n            deleteTask(task._id);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setEditData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    if (isEditing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"border rounded-lg p-4 shadow-sm transition-colors duration-300 \".concat(isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        name: \"title\",\n                        value: editData.title,\n                        onChange: handleChange,\n                        className: \"w-full px-0 py-1 text-lg font-medium border-0 border-b bg-transparent focus:outline-none focus:border-blue-500 transition-colors duration-300 \".concat(isDark ? 'border-gray-600 text-white' : 'border-gray-200 text-gray-900'),\n                        placeholder: \"Task title\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        name: \"description\",\n                        value: editData.description,\n                        onChange: handleChange,\n                        rows: 2,\n                        className: \"w-full px-0 py-1 border-0 bg-transparent focus:outline-none resize-none transition-colors duration-300 \".concat(isDark ? 'text-gray-300' : 'text-gray-600'),\n                        placeholder: \"Add description...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3 pt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                name: \"assignedTo\",\n                                value: editData.assignedTo,\n                                onChange: handleChange,\n                                className: \"flex-1 px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors duration-300 \".concat(isDark ? 'border-gray-600 bg-gray-700 text-white' : 'border-gray-200 bg-white text-gray-900'),\n                                placeholder: \"Assigned to\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                name: \"status\",\n                                value: editData.status,\n                                onChange: handleChange,\n                                className: \"px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors duration-300 \".concat(isDark ? 'border-gray-600 bg-gray-700 text-white' : 'border-gray-200 bg-white text-gray-900'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"To Do\",\n                                        children: \"To Do\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"In Progress\",\n                                        children: \"In Progress\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"Done\",\n                                        children: \"Done\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 pt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSave,\n                                className: \"btn-primary bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors\",\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCancel,\n                                className: \"px-3 py-1 rounded text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors \".concat(isDark ? 'bg-gray-700 text-white hover:bg-gray-600 focus:ring-gray-500' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500'),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"task-item border rounded-lg p-4 hover:shadow-md hover-lift transition-all duration-200 group \".concat(isDark ? 'bg-gray-800 border-gray-700 hover:border-gray-600' : 'bg-white border-gray-200 hover:border-gray-300'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-start mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base font-medium leading-tight transition-colors duration-300 \".concat(isDark ? 'text-white' : 'text-gray-900'),\n                        children: task.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleEdit,\n                                className: \"p-1 rounded transition-colors duration-300 \".concat(isDark ? 'text-gray-500 hover:text-blue-400' : 'text-gray-400 hover:text-blue-600'),\n                                title: \"Edit task\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDelete,\n                                className: \"p-1 rounded transition-colors duration-300 \".concat(isDark ? 'text-gray-500 hover:text-red-400' : 'text-gray-400 hover:text-red-600'),\n                                title: \"Delete task\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, undefined),\n            task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm mb-3 leading-relaxed transition-colors duration-300 \".concat(isDark ? 'text-gray-300' : 'text-gray-600'),\n                children: task.description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-6 h-6 rounded-full flex items-center justify-center transition-colors duration-300 \".concat(isDark ? 'bg-gray-700' : 'bg-gray-200'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium transition-colors duration-300 \".concat(isDark ? 'text-gray-300' : 'text-gray-600'),\n                                    children: task.assignedTo.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm transition-colors duration-300 \".concat(isDark ? 'text-gray-300' : 'text-gray-600'),\n                                children: task.assignedTo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: task.status,\n                        onChange: (e)=>handleStatusChange(e.target.value),\n                        className: \"text-xs px-2 py-1 rounded-full border-0 \".concat(getStatusColor(task.status), \" focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"To Do\",\n                                children: \"To Do\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"In Progress\",\n                                children: \"In Progress\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"Done\",\n                                children: \"Done\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs mt-3 pt-2 border-t transition-colors duration-300 \".concat(isDark ? 'text-gray-500 border-gray-700' : 'text-gray-400 border-gray-100'),\n                children: [\n                    \"Updated \",\n                    new Date(task.updatedAt).toLocaleDateString()\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskItem.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TaskItem, \"eNno7dLR7NeiZ+OTr6dRZzlEX40=\", false, function() {\n    return [\n        _contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__.useTask,\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme\n    ];\n});\n_c = TaskItem;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaskItem);\nvar _c;\n$RefreshReg$(_c, \"TaskItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TaskItem.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/TaskList.tsx":
/*!*************************************!*\
  !*** ./src/components/TaskList.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TaskContext */ \"(app-pages-browser)/./src/contexts/TaskContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(app-pages-browser)/./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _TaskItem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TaskItem */ \"(app-pages-browser)/./src/components/TaskItem.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst TaskList = ()=>{\n    _s();\n    const { state, setFilters, clearFilters, getFilteredTasks } = (0,_contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__.useTask)();\n    const { isDark } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [assigneeFilter, setAssigneeFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const filteredTasks = getFilteredTasks().filter((task)=>{\n        if (!searchQuery) return true;\n        const query = searchQuery.toLowerCase();\n        return task.title.toLowerCase().includes(query) || task.description.toLowerCase().includes(query);\n    });\n    const handleStatusFilterChange = (status)=>{\n        setStatusFilter(status);\n        const newFilters = {\n            ...state.filters,\n            status: status || undefined\n        };\n        if (!status) {\n            delete newFilters.status;\n        }\n        setFilters(newFilters);\n    };\n    const handleAssigneeFilterChange = (assignee)=>{\n        setAssigneeFilter(assignee);\n        const newFilters = {\n            ...state.filters,\n            assignedTo: assignee || undefined\n        };\n        if (!assignee) {\n            delete newFilters.assignedTo;\n        }\n        setFilters(newFilters);\n    };\n    const handleClearFilters = ()=>{\n        setStatusFilter('');\n        setAssigneeFilter('');\n        setSearchQuery('');\n        clearFilters();\n    };\n    const uniqueAssignees = Array.from(new Set(state.tasks.map((task)=>task.assignedTo)));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-3 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-4 w-4 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search tasks...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors duration-300 \".concat(isDark ? 'border-gray-600 bg-gray-800 text-white placeholder-gray-400' : 'border-gray-200 bg-white text-gray-900 placeholder-gray-500')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: statusFilter,\n                                        onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                        className: \"px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors duration-300 \".concat(isDark ? 'border-gray-600 bg-gray-800 text-white' : 'border-gray-200 bg-white text-gray-900'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"All Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"To Do\",\n                                                children: \"To Do\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"In Progress\",\n                                                children: \"In Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Done\",\n                                                children: \"Done\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: assigneeFilter,\n                                        onChange: (e)=>handleAssigneeFilterChange(e.target.value),\n                                        className: \"px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors duration-300 \".concat(isDark ? 'border-gray-600 bg-gray-800 text-white' : 'border-gray-200 bg-white text-gray-900'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"All People\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            uniqueAssignees.map((assignee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: assignee,\n                                                    children: assignee\n                                                }, assignee, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (statusFilter || assigneeFilter || searchQuery) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleClearFilters,\n                                        className: \"px-3 py-2 text-sm transition-colors duration-300 \".concat(isDark ? 'text-gray-400 hover:text-gray-200' : 'text-gray-500 hover:text-gray-700'),\n                                        title: \"Clear filters\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm transition-colors duration-300 \".concat(isDark ? 'text-gray-400' : 'text-gray-500'),\n                        children: [\n                            filteredTasks.length,\n                            \" \",\n                            filteredTasks.length === 1 ? 'task' : 'tasks',\n                            (statusFilter || assigneeFilter || searchQuery) && \" of \".concat(state.tasks.length, \" total\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined),\n            state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 border rounded-md text-sm transition-colors duration-300 \".concat(isDark ? 'bg-red-900 border-red-700 text-red-300' : 'bg-red-50 border-red-200 text-red-700'),\n                children: state.error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, undefined),\n            state.loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 text-sm transition-colors duration-300 \".concat(isDark ? 'text-gray-400' : 'text-gray-600'),\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, undefined),\n            !state.loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                children: filteredTasks.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"col-span-full text-center py-12 text-gray-500\",\n                    children: state.tasks.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"mx-auto h-12 w-12 text-gray-300 mb-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 1,\n                                    d: \"M9 5H7a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 21\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 19\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-medium text-gray-900 mb-1\",\n                                children: \"No tasks yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 19\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Create your first task to get started\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 19\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 17\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"mx-auto h-12 w-12 text-gray-300 mb-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 1,\n                                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 21\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 19\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-medium text-gray-900 mb-1\",\n                                children: \"No tasks found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 19\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Try adjusting your search or filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 19\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 13\n                }, undefined) : filteredTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskItem__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        task: task\n                    }, task._id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 15\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TaskList, \"oDVqzY7KVQkHYUf1ROkPajIkZZw=\", false, function() {\n    return [\n        _contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__.useTask,\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme\n    ];\n});\n_c = TaskList;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaskList);\nvar _c;\n$RefreshReg$(_c, \"TaskList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TaskList.tsx\n"));

/***/ })

});