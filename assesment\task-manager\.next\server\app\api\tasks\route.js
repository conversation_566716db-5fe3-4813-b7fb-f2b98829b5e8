/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/tasks/route";
exports.ids = ["app/api/tasks/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftasks%2Froute&page=%2Fapi%2Ftasks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftasks%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cassesment%5Ctask-manager%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cassesment%5Ctask-manager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftasks%2Froute&page=%2Fapi%2Ftasks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftasks%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cassesment%5Ctask-manager%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cassesment%5Ctask-manager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_arkit_Desktop_assesment_task_manager_src_app_api_tasks_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/tasks/route.ts */ \"(rsc)/./src/app/api/tasks/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/tasks/route\",\n        pathname: \"/api/tasks\",\n        filename: \"route\",\n        bundlePath: \"app/api/tasks/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\app\\\\api\\\\tasks\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_arkit_Desktop_assesment_task_manager_src_app_api_tasks_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftasks%2Froute&page=%2Fapi%2Ftasks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftasks%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cassesment%5Ctask-manager%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cassesment%5Ctask-manager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/tasks/route.ts":
/*!************************************!*\
  !*** ./src/app/api/tasks/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_Task__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/models/Task */ \"(rsc)/./src/models/Task.ts\");\n\n\n\n// GET /api/tasks - Get all tasks with optional filtering\nasync function GET(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const { searchParams } = new URL(request.url);\n        const status = searchParams.get('status');\n        const assignedTo = searchParams.get('assignedTo');\n        // Build filter object\n        const filter = {};\n        if (status) {\n            filter.status = status;\n        }\n        if (assignedTo) {\n            filter.assignedTo = {\n                $regex: assignedTo,\n                $options: 'i'\n            };\n        }\n        const tasks = await _models_Task__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find(filter).sort({\n            createdAt: -1\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: tasks\n        });\n    } catch (error) {\n        console.error('Error fetching tasks:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch tasks'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/tasks - Create a new task\nasync function POST(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const body = await request.json();\n        const { title, description, assignedTo, status } = body;\n        // Validate required fields\n        if (!title || !description || !assignedTo) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Title, description, and assignedTo are required'\n            }, {\n                status: 400\n            });\n        }\n        const task = await _models_Task__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n            title,\n            description,\n            assignedTo,\n            status: status || 'To Do'\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: task\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating task:', error);\n        // Handle validation errors\n        if (error.name === 'ValidationError') {\n            const validationErrors = Object.values(error.errors).map((err)=>err.message);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Validation failed',\n                details: validationErrors\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to create task'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/tasks/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Task.ts":
/*!****************************!*\
  !*** ./src/models/Task.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst TaskSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    title: {\n        type: String,\n        required: [\n            true,\n            'Title is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Title cannot be more than 100 characters'\n        ]\n    },\n    description: {\n        type: String,\n        required: [\n            true,\n            'Description is required'\n        ],\n        trim: true,\n        maxlength: [\n            500,\n            'Description cannot be more than 500 characters'\n        ]\n    },\n    assignedTo: {\n        type: String,\n        required: [\n            true,\n            'Assigned To is required'\n        ],\n        trim: true,\n        maxlength: [\n            50,\n            'Assigned To cannot be more than 50 characters'\n        ]\n    },\n    status: {\n        type: String,\n        required: [\n            true,\n            'Status is required'\n        ],\n        enum: {\n            values: [\n                'To Do',\n                'In Progress',\n                'Done'\n            ],\n            message: 'Status must be either To Do, In Progress, or Done'\n        },\n        default: 'To Do'\n    }\n}, {\n    timestamps: true\n});\n// Add indexes for better query performance\nTaskSchema.index({\n    status: 1\n});\nTaskSchema.index({\n    assignedTo: 1\n});\nTaskSchema.index({\n    createdAt: -1\n});\n// Prevent model re-compilation during development\nconst Task = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Task || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('Task', TaskSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Task);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Task.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftasks%2Froute&page=%2Fapi%2Ftasks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftasks%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cassesment%5Ctask-manager%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cassesment%5Ctask-manager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();