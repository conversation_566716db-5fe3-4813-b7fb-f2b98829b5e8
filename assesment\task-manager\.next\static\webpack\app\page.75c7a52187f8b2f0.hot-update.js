"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/TaskForm.tsx":
/*!*************************************!*\
  !*** ./src/components/TaskForm.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TaskContext */ \"(app-pages-browser)/./src/contexts/TaskContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(app-pages-browser)/./src/contexts/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst TaskForm = (param)=>{\n    let { onClose } = param;\n    _s();\n    const { addTask } = (0,_contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__.useTask)();\n    const { isDark } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        description: '',\n        assignedTo: '',\n        status: 'To Do'\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) {\n            newErrors.title = 'Title is required';\n        }\n        if (!formData.description.trim()) {\n            newErrors.description = 'Description is required';\n        }\n        if (!formData.assignedTo.trim()) {\n            newErrors.assignedTo = 'Assigned To is required';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (validateForm()) {\n            addTask(formData);\n            setFormData({\n                title: '',\n                description: '',\n                assignedTo: '',\n                status: 'To Do'\n            });\n            setErrors({});\n            if (onClose) {\n                onClose();\n            }\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: ''\n                }));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border rounded-lg p-6 shadow-xl backdrop-blur-sm transition-colors duration-300 \".concat(isDark ? 'bg-black bg-opacity-95 border-gray-800' : 'bg-white bg-opacity-90 border-gray-200'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold \".concat(isDark ? 'text-white' : 'text-gray-900'),\n                        children: \"Create New Task\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"p-1 rounded-md transition-colors duration-300 \".concat(isDark ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-900' : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'),\n                        title: \"Close\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"title\",\n                                name: \"title\",\n                                value: formData.title,\n                                onChange: handleChange,\n                                className: \"w-full px-0 py-2 text-lg font-medium border-0 border-b-2 bg-transparent focus:outline-none focus:border-blue-500 transition-colors duration-300 \".concat(errors.title ? 'border-red-500' : isDark ? 'border-gray-600 text-white' : 'border-gray-200 text-gray-900'),\n                                placeholder: \"Task title\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, undefined),\n                            errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-500 text-sm mt-1\",\n                                children: errors.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 28\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"description\",\n                                name: \"description\",\n                                value: formData.description,\n                                onChange: handleChange,\n                                rows: 3,\n                                className: \"w-full px-0 py-2 border-0 bg-transparent focus:outline-none resize-none transition-colors duration-300 \".concat(errors.description ? 'text-red-500' : isDark ? 'text-gray-300' : 'text-gray-700'),\n                                placeholder: \"Add a description...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined),\n                            errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-500 text-sm mt-1\",\n                                children: errors.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 34\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"assignedTo\",\n                                        name: \"assignedTo\",\n                                        value: formData.assignedTo,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors duration-300 \".concat(errors.assignedTo ? 'border-red-500' : isDark ? 'border-gray-600 bg-gray-700 text-white' : 'border-gray-200 bg-white text-gray-900'),\n                                        placeholder: \"Assigned to\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    errors.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-500 text-xs mt-1\",\n                                        children: errors.assignedTo\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 35\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"status\",\n                                    name: \"status\",\n                                    value: formData.status,\n                                    onChange: handleChange,\n                                    className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors duration-300 \".concat(isDark ? 'border-gray-600 bg-gray-700 text-white' : 'border-gray-200 bg-white text-gray-900'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"To Do\",\n                                            children: \"To Do\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"In Progress\",\n                                            children: \"In Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"Done\",\n                                            children: \"Done\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors text-sm font-medium\",\n                            children: \"Create Task\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskForm.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TaskForm, \"WxyQSlI8UoCa9/6a8UvTignhR4A=\", false, function() {\n    return [\n        _contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__.useTask,\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme\n    ];\n});\n_c = TaskForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaskForm);\nvar _c;\n$RefreshReg$(_c, \"TaskForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TaskForm.tsx\n"));

/***/ })

});