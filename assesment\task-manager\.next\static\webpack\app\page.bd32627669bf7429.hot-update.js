"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/TaskList.tsx":
/*!*************************************!*\
  !*** ./src/components/TaskList.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TaskContext */ \"(app-pages-browser)/./src/contexts/TaskContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(app-pages-browser)/./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _TaskItem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TaskItem */ \"(app-pages-browser)/./src/components/TaskItem.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst TaskList = ()=>{\n    _s();\n    const { state, setFilters, clearFilters, getFilteredTasks } = (0,_contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__.useTask)();\n    const { isDark } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [assigneeFilter, setAssigneeFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const filteredTasks = getFilteredTasks().filter((task)=>{\n        if (!searchQuery) return true;\n        const query = searchQuery.toLowerCase();\n        return task.title.toLowerCase().includes(query) || task.description.toLowerCase().includes(query);\n    });\n    const handleStatusFilterChange = (status)=>{\n        setStatusFilter(status);\n        const newFilters = {\n            ...state.filters,\n            status: status || undefined\n        };\n        if (!status) {\n            delete newFilters.status;\n        }\n        setFilters(newFilters);\n    };\n    const handleAssigneeFilterChange = (assignee)=>{\n        setAssigneeFilter(assignee);\n        const newFilters = {\n            ...state.filters,\n            assignedTo: assignee || undefined\n        };\n        if (!assignee) {\n            delete newFilters.assignedTo;\n        }\n        setFilters(newFilters);\n    };\n    const handleClearFilters = ()=>{\n        setStatusFilter('');\n        setAssigneeFilter('');\n        setSearchQuery('');\n        clearFilters();\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'To Do':\n                return 'bg-gray-100 text-gray-800';\n            case 'In Progress':\n                return 'bg-blue-100 text-blue-800';\n            case 'Done':\n                return 'bg-green-100 text-green-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const uniqueAssignees = Array.from(new Set(state.tasks.map((task)=>task.assignedTo)));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-3 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-4 w-4 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search tasks...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors duration-300 \".concat(isDark ? 'border-gray-600 bg-gray-800 text-white placeholder-gray-400' : 'border-gray-200 bg-white text-gray-900 placeholder-gray-500')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: statusFilter,\n                                        onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                        className: \"px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors duration-300 \".concat(isDark ? 'border-gray-600 bg-gray-800 text-white' : 'border-gray-200 bg-white text-gray-900'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"All Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"To Do\",\n                                                children: \"To Do\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"In Progress\",\n                                                children: \"In Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Done\",\n                                                children: \"Done\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: assigneeFilter,\n                                        onChange: (e)=>handleAssigneeFilterChange(e.target.value),\n                                        className: \"px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors duration-300 \".concat(isDark ? 'border-gray-600 bg-gray-800 text-white' : 'border-gray-200 bg-white text-gray-900'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"All People\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            uniqueAssignees.map((assignee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: assignee,\n                                                    children: assignee\n                                                }, assignee, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (statusFilter || assigneeFilter || searchQuery) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleClearFilters,\n                                        className: \"px-3 py-2 text-sm transition-colors duration-300 \".concat(isDark ? 'text-gray-400 hover:text-gray-200' : 'text-gray-500 hover:text-gray-700'),\n                                        title: \"Clear filters\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm transition-colors duration-300 \".concat(isDark ? 'text-gray-400' : 'text-gray-500'),\n                        children: [\n                            filteredTasks.length,\n                            \" \",\n                            filteredTasks.length === 1 ? 'task' : 'tasks',\n                            (statusFilter || assigneeFilter || searchQuery) && \" of \".concat(state.tasks.length, \" total\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 border rounded-md text-sm transition-colors duration-300 \".concat(isDark ? 'bg-red-900 border-red-700 text-red-300' : 'bg-red-50 border-red-200 text-red-700'),\n                children: state.error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, undefined),\n            state.loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 text-gray-600 text-sm\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, undefined),\n            !state.loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                children: filteredTasks.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"col-span-full text-center py-12 text-gray-500\",\n                    children: state.tasks.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"mx-auto h-12 w-12 text-gray-300 mb-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 1,\n                                    d: \"M9 5H7a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 21\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 19\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-medium text-gray-900 mb-1\",\n                                children: \"No tasks yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 19\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Create your first task to get started\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 19\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 17\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"mx-auto h-12 w-12 text-gray-300 mb-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 1,\n                                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 21\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 19\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-medium text-gray-900 mb-1\",\n                                children: \"No tasks found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 19\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Try adjusting your search or filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 19\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 13\n                }, undefined) : filteredTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskItem__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        task: task\n                    }, task._id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 15\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\assesment\\\\task-manager\\\\src\\\\components\\\\TaskList.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TaskList, \"oDVqzY7KVQkHYUf1ROkPajIkZZw=\", false, function() {\n    return [\n        _contexts_TaskContext__WEBPACK_IMPORTED_MODULE_2__.useTask,\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme\n    ];\n});\n_c = TaskList;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaskList);\nvar _c;\n$RefreshReg$(_c, \"TaskList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1Rhc2tMaXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFd0M7QUFDUztBQUNFO0FBRWpCO0FBRWxDLE1BQU1LLFdBQXFCOztJQUN6QixNQUFNLEVBQUVDLEtBQUssRUFBRUMsVUFBVSxFQUFFQyxZQUFZLEVBQUVDLGdCQUFnQixFQUFFLEdBQUdQLDhEQUFPQTtJQUNyRSxNQUFNLEVBQUVRLE1BQU0sRUFBRSxHQUFHUCxnRUFBUUE7SUFDM0IsTUFBTSxDQUFDUSxjQUFjQyxnQkFBZ0IsR0FBR1gsK0NBQVFBLENBQWtCO0lBQ2xFLE1BQU0sQ0FBQ1ksZ0JBQWdCQyxrQkFBa0IsR0FBR2IsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDYyxhQUFhQyxlQUFlLEdBQUdmLCtDQUFRQSxDQUFDO0lBRS9DLE1BQU1nQixnQkFBZ0JSLG1CQUFtQlMsTUFBTSxDQUFDQyxDQUFBQTtRQUM5QyxJQUFJLENBQUNKLGFBQWEsT0FBTztRQUN6QixNQUFNSyxRQUFRTCxZQUFZTSxXQUFXO1FBQ3JDLE9BQU9GLEtBQUtHLEtBQUssQ0FBQ0QsV0FBVyxHQUFHRSxRQUFRLENBQUNILFVBQ2xDRCxLQUFLSyxXQUFXLENBQUNILFdBQVcsR0FBR0UsUUFBUSxDQUFDSDtJQUNqRDtJQUVBLE1BQU1LLDJCQUEyQixDQUFDQztRQUNoQ2QsZ0JBQWdCYztRQUNoQixNQUFNQyxhQUFhO1lBQ2pCLEdBQUdyQixNQUFNc0IsT0FBTztZQUNoQkYsUUFBUUEsVUFBVUc7UUFDcEI7UUFDQSxJQUFJLENBQUNILFFBQVE7WUFDWCxPQUFPQyxXQUFXRCxNQUFNO1FBQzFCO1FBQ0FuQixXQUFXb0I7SUFDYjtJQUVBLE1BQU1HLDZCQUE2QixDQUFDQztRQUNsQ2pCLGtCQUFrQmlCO1FBQ2xCLE1BQU1KLGFBQWE7WUFDakIsR0FBR3JCLE1BQU1zQixPQUFPO1lBQ2hCSSxZQUFZRCxZQUFZRjtRQUMxQjtRQUNBLElBQUksQ0FBQ0UsVUFBVTtZQUNiLE9BQU9KLFdBQVdLLFVBQVU7UUFDOUI7UUFDQXpCLFdBQVdvQjtJQUNiO0lBRUEsTUFBTU0scUJBQXFCO1FBQ3pCckIsZ0JBQWdCO1FBQ2hCRSxrQkFBa0I7UUFDbEJFLGVBQWU7UUFDZlI7SUFDRjtJQUVBLE1BQU0wQixpQkFBaUIsQ0FBQ1I7UUFDdEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLE1BQU1TLGtCQUFrQkMsTUFBTUMsSUFBSSxDQUFDLElBQUlDLElBQUloQyxNQUFNaUMsS0FBSyxDQUFDQyxHQUFHLENBQUNyQixDQUFBQSxPQUFRQSxLQUFLYSxVQUFVO0lBRWxGLHFCQUNFLDhEQUFDUzs7MEJBRUMsOERBQUNBO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDQzs0Q0FBSUQsV0FBVTs0Q0FBd0JFLE1BQUs7NENBQU9DLFFBQU87NENBQWVDLFNBQVE7c0RBQy9FLDRFQUFDQztnREFBS0MsZUFBYztnREFBUUMsZ0JBQWU7Z0RBQVFDLGFBQWE7Z0RBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR3pFLDhEQUFDQzt3Q0FDQ0MsTUFBSzt3Q0FDTEMsYUFBWTt3Q0FDWkMsT0FBT3hDO3dDQUNQeUMsVUFBVSxDQUFDQyxJQUFNekMsZUFBZXlDLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3Q0FDOUNiLFdBQVcsNkpBSVYsT0FIQ2hDLFNBQ0ksZ0VBQ0E7Ozs7Ozs7Ozs7OzswQ0FNViw4REFBQytCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2lCO3dDQUNDSixPQUFPNUM7d0NBQ1A2QyxVQUFVLENBQUNDLElBQU1oQyx5QkFBeUJnQyxFQUFFQyxNQUFNLENBQUNILEtBQUs7d0NBQ3hEYixXQUFXLGdKQUlWLE9BSENoQyxTQUNJLDJDQUNBOzswREFHTiw4REFBQ2tEO2dEQUFPTCxPQUFNOzBEQUFHOzs7Ozs7MERBQ2pCLDhEQUFDSztnREFBT0wsT0FBTTswREFBUTs7Ozs7OzBEQUN0Qiw4REFBQ0s7Z0RBQU9MLE9BQU07MERBQWM7Ozs7OzswREFDNUIsOERBQUNLO2dEQUFPTCxPQUFNOzBEQUFPOzs7Ozs7Ozs7Ozs7a0RBR3ZCLDhEQUFDSTt3Q0FDQ0osT0FBTzFDO3dDQUNQMkMsVUFBVSxDQUFDQyxJQUFNM0IsMkJBQTJCMkIsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO3dDQUMxRGIsV0FBVyxnSkFJVixPQUhDaEMsU0FDSSwyQ0FDQTs7MERBR04sOERBQUNrRDtnREFBT0wsT0FBTTswREFBRzs7Ozs7OzRDQUNoQnBCLGdCQUFnQkssR0FBRyxDQUFDVCxDQUFBQSx5QkFDbkIsOERBQUM2QjtvREFBc0JMLE9BQU94Qjs4REFDM0JBO21EQURVQTs7Ozs7Ozs7Ozs7b0NBTWZwQixDQUFBQSxnQkFBZ0JFLGtCQUFrQkUsV0FBVSxtQkFDNUMsOERBQUM4Qzt3Q0FDQ0MsU0FBUzdCO3dDQUNUUyxXQUFXLG9EQUlWLE9BSENoQyxTQUNJLHNDQUNBO3dDQUVOWSxPQUFNO2tEQUNQOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUVAsOERBQUNtQjt3QkFBSUMsV0FBVywwQ0FFZixPQURDaEMsU0FBUyxrQkFBa0I7OzRCQUUxQk8sY0FBYzhDLE1BQU07NEJBQUM7NEJBQUU5QyxjQUFjOEMsTUFBTSxLQUFLLElBQUksU0FBUzs0QkFDNURwRCxDQUFBQSxnQkFBZ0JFLGtCQUFrQkUsV0FBVSxLQUFNLE9BQTBCLE9BQW5CVCxNQUFNaUMsS0FBSyxDQUFDd0IsTUFBTSxFQUFDOzs7Ozs7Ozs7Ozs7O1lBS2pGekQsTUFBTTBELEtBQUssa0JBQ1YsOERBQUN2QjtnQkFBSUMsV0FBVyxxRUFJZixPQUhDaEMsU0FDSSwyQ0FDQTswQkFFSEosTUFBTTBELEtBQUs7Ozs7OztZQUtmMUQsTUFBTTJELE9BQU8sa0JBQ1osOERBQUN4QjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUN3Qjt3QkFBS3hCLFdBQVU7a0NBQTZCOzs7Ozs7Ozs7Ozs7WUFLaEQsQ0FBQ3BDLE1BQU0yRCxPQUFPLGtCQUNiLDhEQUFDeEI7Z0JBQUlDLFdBQVU7MEJBQ1p6QixjQUFjOEMsTUFBTSxLQUFLLGtCQUN4Qiw4REFBQ3RCO29CQUFJQyxXQUFVOzhCQUNacEMsTUFBTWlDLEtBQUssQ0FBQ3dCLE1BQU0sS0FBSyxrQkFDdEIsOERBQUN0Qjs7MENBQ0MsOERBQUNFO2dDQUFJRCxXQUFVO2dDQUF1Q0UsTUFBSztnQ0FBT0MsUUFBTztnQ0FBZUMsU0FBUTswQ0FDOUYsNEVBQUNDO29DQUFLQyxlQUFjO29DQUFRQyxnQkFBZTtvQ0FBUUMsYUFBYTtvQ0FBR0MsR0FBRTs7Ozs7Ozs7Ozs7MENBRXZFLDhEQUFDZ0I7Z0NBQUV6QixXQUFVOzBDQUF5Qzs7Ozs7OzBDQUN0RCw4REFBQ3lCO2dDQUFFekIsV0FBVTswQ0FBZ0I7Ozs7Ozs7Ozs7O2tEQUcvQiw4REFBQ0Q7OzBDQUNDLDhEQUFDRTtnQ0FBSUQsV0FBVTtnQ0FBdUNFLE1BQUs7Z0NBQU9DLFFBQU87Z0NBQWVDLFNBQVE7MENBQzlGLDRFQUFDQztvQ0FBS0MsZUFBYztvQ0FBUUMsZ0JBQWU7b0NBQVFDLGFBQWE7b0NBQUdDLEdBQUU7Ozs7Ozs7Ozs7OzBDQUV2RSw4REFBQ2dCO2dDQUFFekIsV0FBVTswQ0FBeUM7Ozs7OzswQ0FDdEQsOERBQUN5QjtnQ0FBRXpCLFdBQVU7MENBQWdCOzs7Ozs7Ozs7Ozs7Ozs7O2dDQUtuQ3pCLGNBQWN1QixHQUFHLENBQUNyQixDQUFBQSxxQkFDaEIsOERBQUNmLGlEQUFRQTt3QkFBZ0JlLE1BQU1BO3VCQUFoQkEsS0FBS2lELEdBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPckM7R0FuTU0vRDs7UUFDMERILDBEQUFPQTtRQUNsREMsNERBQVFBOzs7S0FGdkJFO0FBcU1OLGlFQUFlQSxRQUFRQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFya2l0XFxEZXNrdG9wXFxhc3Nlc21lbnRcXHRhc2stbWFuYWdlclxcc3JjXFxjb21wb25lbnRzXFxUYXNrTGlzdC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VUYXNrIH0gZnJvbSAnQC9jb250ZXh0cy9UYXNrQ29udGV4dCc7XG5pbXBvcnQgeyB1c2VUaGVtZSB9IGZyb20gJ0AvY29udGV4dHMvVGhlbWVDb250ZXh0JztcbmltcG9ydCB7IFRhc2tTdGF0dXMgfSBmcm9tICdAL3R5cGVzJztcbmltcG9ydCBUYXNrSXRlbSBmcm9tICcuL1Rhc2tJdGVtJztcblxuY29uc3QgVGFza0xpc3Q6IFJlYWN0LkZDID0gKCkgPT4ge1xuICBjb25zdCB7IHN0YXRlLCBzZXRGaWx0ZXJzLCBjbGVhckZpbHRlcnMsIGdldEZpbHRlcmVkVGFza3MgfSA9IHVzZVRhc2soKTtcbiAgY29uc3QgeyBpc0RhcmsgfSA9IHVzZVRoZW1lKCk7XG4gIGNvbnN0IFtzdGF0dXNGaWx0ZXIsIHNldFN0YXR1c0ZpbHRlcl0gPSB1c2VTdGF0ZTxUYXNrU3RhdHVzIHwgJyc+KCcnKTtcbiAgY29uc3QgW2Fzc2lnbmVlRmlsdGVyLCBzZXRBc3NpZ25lZUZpbHRlcl0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtzZWFyY2hRdWVyeSwgc2V0U2VhcmNoUXVlcnldID0gdXNlU3RhdGUoJycpO1xuXG4gIGNvbnN0IGZpbHRlcmVkVGFza3MgPSBnZXRGaWx0ZXJlZFRhc2tzKCkuZmlsdGVyKHRhc2sgPT4ge1xuICAgIGlmICghc2VhcmNoUXVlcnkpIHJldHVybiB0cnVlO1xuICAgIGNvbnN0IHF1ZXJ5ID0gc2VhcmNoUXVlcnkudG9Mb3dlckNhc2UoKTtcbiAgICByZXR1cm4gdGFzay50aXRsZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHF1ZXJ5KSB8fFxuICAgICAgICAgICB0YXNrLmRlc2NyaXB0aW9uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMocXVlcnkpO1xuICB9KTtcblxuICBjb25zdCBoYW5kbGVTdGF0dXNGaWx0ZXJDaGFuZ2UgPSAoc3RhdHVzOiBUYXNrU3RhdHVzIHwgJycpID0+IHtcbiAgICBzZXRTdGF0dXNGaWx0ZXIoc3RhdHVzKTtcbiAgICBjb25zdCBuZXdGaWx0ZXJzID0ge1xuICAgICAgLi4uc3RhdGUuZmlsdGVycyxcbiAgICAgIHN0YXR1czogc3RhdHVzIHx8IHVuZGVmaW5lZCxcbiAgICB9O1xuICAgIGlmICghc3RhdHVzKSB7XG4gICAgICBkZWxldGUgbmV3RmlsdGVycy5zdGF0dXM7XG4gICAgfVxuICAgIHNldEZpbHRlcnMobmV3RmlsdGVycyk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQXNzaWduZWVGaWx0ZXJDaGFuZ2UgPSAoYXNzaWduZWU6IHN0cmluZykgPT4ge1xuICAgIHNldEFzc2lnbmVlRmlsdGVyKGFzc2lnbmVlKTtcbiAgICBjb25zdCBuZXdGaWx0ZXJzID0ge1xuICAgICAgLi4uc3RhdGUuZmlsdGVycyxcbiAgICAgIGFzc2lnbmVkVG86IGFzc2lnbmVlIHx8IHVuZGVmaW5lZCxcbiAgICB9O1xuICAgIGlmICghYXNzaWduZWUpIHtcbiAgICAgIGRlbGV0ZSBuZXdGaWx0ZXJzLmFzc2lnbmVkVG87XG4gICAgfVxuICAgIHNldEZpbHRlcnMobmV3RmlsdGVycyk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ2xlYXJGaWx0ZXJzID0gKCkgPT4ge1xuICAgIHNldFN0YXR1c0ZpbHRlcignJyk7XG4gICAgc2V0QXNzaWduZWVGaWx0ZXIoJycpO1xuICAgIHNldFNlYXJjaFF1ZXJ5KCcnKTtcbiAgICBjbGVhckZpbHRlcnMoKTtcbiAgfTtcblxuICBjb25zdCBnZXRTdGF0dXNDb2xvciA9IChzdGF0dXM6IFRhc2tTdGF0dXMpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnVG8gRG8nOlxuICAgICAgICByZXR1cm4gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnO1xuICAgICAgY2FzZSAnSW4gUHJvZ3Jlc3MnOlxuICAgICAgICByZXR1cm4gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAnO1xuICAgICAgY2FzZSAnRG9uZSc6XG4gICAgICAgIHJldHVybiAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJztcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCc7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHVuaXF1ZUFzc2lnbmVlcyA9IEFycmF5LmZyb20obmV3IFNldChzdGF0ZS50YXNrcy5tYXAodGFzayA9PiB0YXNrLmFzc2lnbmVkVG8pKSk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2PlxuICAgICAgey8qIFNlYXJjaCBhbmQgRmlsdGVycyBCYXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGdhcC0zIG1iLTRcIj5cbiAgICAgICAgICB7LyogU2VhcmNoICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LXktMCBsZWZ0LTAgcGwtMyBmbGV4IGl0ZW1zLWNlbnRlciBwb2ludGVyLWV2ZW50cy1ub25lXCI+XG4gICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNDAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTIxIDIxbC02LTZtMi01YTcgNyAwIDExLTE0IDAgNyA3IDAgMDExNCAwelwiIC8+XG4gICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCB0YXNrcy4uLlwiXG4gICAgICAgICAgICAgIHZhbHVlPXtzZWFyY2hRdWVyeX1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hRdWVyeShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBwbC0xMCBwci00IHB5LTIgYm9yZGVyIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMCB0ZXh0LXNtIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgICAgICAgIGlzRGFya1xuICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWdyYXktNjAwIGJnLWdyYXktODAwIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDAnXG4gICAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS0yMDAgYmctd2hpdGUgdGV4dC1ncmF5LTkwMCBwbGFjZWhvbGRlci1ncmF5LTUwMCdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEZpbHRlcnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgIHZhbHVlPXtzdGF0dXNGaWx0ZXJ9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU3RhdHVzRmlsdGVyQ2hhbmdlKGUudGFyZ2V0LnZhbHVlIGFzIFRhc2tTdGF0dXMgfCAnJyl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTMgcHktMiBib3JkZXIgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0xIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwIHRleHQtc20gdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgaXNEYXJrXG4gICAgICAgICAgICAgICAgICA/ICdib3JkZXItZ3JheS02MDAgYmctZ3JheS04MDAgdGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTIwMCBiZy13aGl0ZSB0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPkFsbCBTdGF0dXM8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlRvIERvXCI+VG8gRG88L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkluIFByb2dyZXNzXCI+SW4gUHJvZ3Jlc3M8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkRvbmVcIj5Eb25lPC9vcHRpb24+XG4gICAgICAgICAgICA8L3NlbGVjdD5cblxuICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICB2YWx1ZT17YXNzaWduZWVGaWx0ZXJ9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlQXNzaWduZWVGaWx0ZXJDaGFuZ2UoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0zIHB5LTIgYm9yZGVyIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMCB0ZXh0LXNtIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgICAgICAgIGlzRGFya1xuICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWdyYXktNjAwIGJnLWdyYXktODAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS0yMDAgYmctd2hpdGUgdGV4dC1ncmF5LTkwMCdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5BbGwgUGVvcGxlPC9vcHRpb24+XG4gICAgICAgICAgICAgIHt1bmlxdWVBc3NpZ25lZXMubWFwKGFzc2lnbmVlID0+IChcbiAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17YXNzaWduZWV9IHZhbHVlPXthc3NpZ25lZX0+XG4gICAgICAgICAgICAgICAgICB7YXNzaWduZWV9XG4gICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9zZWxlY3Q+XG5cbiAgICAgICAgICAgIHsoc3RhdHVzRmlsdGVyIHx8IGFzc2lnbmVlRmlsdGVyIHx8IHNlYXJjaFF1ZXJ5KSAmJiAoXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDbGVhckZpbHRlcnN9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMyBweS0yIHRleHQtc20gdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgICBpc0RhcmtcbiAgICAgICAgICAgICAgICAgICAgPyAndGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktMjAwJ1xuICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgdGl0bGU9XCJDbGVhciBmaWx0ZXJzXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIOKclVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBSZXN1bHRzIGNvdW50ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHRleHQtc20gdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgaXNEYXJrID8gJ3RleHQtZ3JheS00MDAnIDogJ3RleHQtZ3JheS01MDAnXG4gICAgICAgIH1gfT5cbiAgICAgICAgICB7ZmlsdGVyZWRUYXNrcy5sZW5ndGh9IHtmaWx0ZXJlZFRhc2tzLmxlbmd0aCA9PT0gMSA/ICd0YXNrJyA6ICd0YXNrcyd9XG4gICAgICAgICAgeyhzdGF0dXNGaWx0ZXIgfHwgYXNzaWduZWVGaWx0ZXIgfHwgc2VhcmNoUXVlcnkpICYmIGAgb2YgJHtzdGF0ZS50YXNrcy5sZW5ndGh9IHRvdGFsYH1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEVycm9yIE1lc3NhZ2UgKi99XG4gICAgICB7c3RhdGUuZXJyb3IgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YG1iLTQgcC0zIGJvcmRlciByb3VuZGVkLW1kIHRleHQtc20gdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgaXNEYXJrXG4gICAgICAgICAgICA/ICdiZy1yZWQtOTAwIGJvcmRlci1yZWQtNzAwIHRleHQtcmVkLTMwMCdcbiAgICAgICAgICAgIDogJ2JnLXJlZC01MCBib3JkZXItcmVkLTIwMCB0ZXh0LXJlZC03MDAnXG4gICAgICAgIH1gfT5cbiAgICAgICAgICB7c3RhdGUuZXJyb3J9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIExvYWRpbmcgSW5kaWNhdG9yICovfVxuICAgICAge3N0YXRlLmxvYWRpbmcgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtYmxvY2sgYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTYgdy02IGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwXCI+PC9kaXY+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiB0ZXh0LWdyYXktNjAwIHRleHQtc21cIj5Mb2FkaW5nLi4uPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBUYXNrIEdyaWQgKi99XG4gICAgICB7IXN0YXRlLmxvYWRpbmcgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICB7ZmlsdGVyZWRUYXNrcy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLWZ1bGwgdGV4dC1jZW50ZXIgcHktMTIgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICB7c3RhdGUudGFza3MubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cIm14LWF1dG8gaC0xMiB3LTEyIHRleHQtZ3JheS0zMDAgbWItNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezF9IGQ9XCJNOSA1SDdhMiAyIDAgMDAtMiAydjExYTIgMiAwIDAwMiAyaDExYTIgMiAwIDAwMi0yVjdhMiAyIDAgMDAtMi0yaC0yTTkgNWEyIDIgMCAwMDIgMmgyYTIgMiAwIDAwMi0yTTkgNWEyIDIgMCAwMTItMmgyYTIgMiAwIDAxMiAyXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTFcIj5ObyB0YXNrcyB5ZXQ8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+Q3JlYXRlIHlvdXIgZmlyc3QgdGFzayB0byBnZXQgc3RhcnRlZDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJteC1hdXRvIGgtMTIgdy0xMiB0ZXh0LWdyYXktMzAwIG1iLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsxfSBkPVwiTTIxIDIxbC02LTZtMi01YTcgNyAwIDExLTE0IDAgNyA3IDAgMDExNCAwelwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0xXCI+Tm8gdGFza3MgZm91bmQ8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+VHJ5IGFkanVzdGluZyB5b3VyIHNlYXJjaCBvciBmaWx0ZXJzPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIGZpbHRlcmVkVGFza3MubWFwKHRhc2sgPT4gKFxuICAgICAgICAgICAgICA8VGFza0l0ZW0ga2V5PXt0YXNrLl9pZH0gdGFzaz17dGFza30gLz5cbiAgICAgICAgICAgICkpXG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgVGFza0xpc3Q7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZVRhc2siLCJ1c2VUaGVtZSIsIlRhc2tJdGVtIiwiVGFza0xpc3QiLCJzdGF0ZSIsInNldEZpbHRlcnMiLCJjbGVhckZpbHRlcnMiLCJnZXRGaWx0ZXJlZFRhc2tzIiwiaXNEYXJrIiwic3RhdHVzRmlsdGVyIiwic2V0U3RhdHVzRmlsdGVyIiwiYXNzaWduZWVGaWx0ZXIiLCJzZXRBc3NpZ25lZUZpbHRlciIsInNlYXJjaFF1ZXJ5Iiwic2V0U2VhcmNoUXVlcnkiLCJmaWx0ZXJlZFRhc2tzIiwiZmlsdGVyIiwidGFzayIsInF1ZXJ5IiwidG9Mb3dlckNhc2UiLCJ0aXRsZSIsImluY2x1ZGVzIiwiZGVzY3JpcHRpb24iLCJoYW5kbGVTdGF0dXNGaWx0ZXJDaGFuZ2UiLCJzdGF0dXMiLCJuZXdGaWx0ZXJzIiwiZmlsdGVycyIsInVuZGVmaW5lZCIsImhhbmRsZUFzc2lnbmVlRmlsdGVyQ2hhbmdlIiwiYXNzaWduZWUiLCJhc3NpZ25lZFRvIiwiaGFuZGxlQ2xlYXJGaWx0ZXJzIiwiZ2V0U3RhdHVzQ29sb3IiLCJ1bmlxdWVBc3NpZ25lZXMiLCJBcnJheSIsImZyb20iLCJTZXQiLCJ0YXNrcyIsIm1hcCIsImRpdiIsImNsYXNzTmFtZSIsInN2ZyIsImZpbGwiLCJzdHJva2UiLCJ2aWV3Qm94IiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJzZWxlY3QiLCJvcHRpb24iLCJidXR0b24iLCJvbkNsaWNrIiwibGVuZ3RoIiwiZXJyb3IiLCJsb2FkaW5nIiwic3BhbiIsInAiLCJfaWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TaskList.tsx\n"));

/***/ })

});